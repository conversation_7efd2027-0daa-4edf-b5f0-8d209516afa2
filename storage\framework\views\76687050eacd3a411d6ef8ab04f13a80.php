<?php if (! $__env->hasRenderedOnce('e8809b7a-034a-4de5-ba9e-e3775df06534')): $__env->markAsRenderedOnce('e8809b7a-034a-4de5-ba9e-e3775df06534'); ?>
    <div
        class="offcanvas offcanvas-end"
        tabindex="-1"
        id="notification-sidebar"
        aria-labelledby="notification-sidebar-label"
        data-url="<?php echo e(route('notifications.index')); ?>"
        data-count-url="<?php echo e(route('notifications.count-unread')); ?>"
    >
        <button
            type="button"
            class="btn-close text-reset"
            data-bs-dismiss="offcanvas"
            aria-label="Close"
        ></button>

        <div class="notification-content"></div>
    </div>

    <script src="<?php echo e(asset('vendor/core/core/base/js/notification.js')); ?>"></script>
<?php endif; ?>
<?php /**PATH D:\DevOps\delwest\app\platform/core/base/resources/views/notification/notification.blade.php ENDPATH**/ ?>