1751267629O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:4:{i:0;O:26:"Botble\ACL\Models\UserMeta":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:9:"user_meta";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:1;s:3:"key";s:10:"theme_mode";s:5:"value";s:4:"dark";s:7:"user_id";i:1;s:10:"created_at";s:19:"2025-06-27 12:47:53";s:10:"updated_at";s:19:"2025-06-29 07:45:27";}s:11:" * original";a:6:{s:2:"id";i:1;s:3:"key";s:10:"theme_mode";s:5:"value";s:4:"dark";s:7:"user_id";i:1;s:10:"created_at";s:19:"2025-06-27 12:47:53";s:10:"updated_at";s:19:"2025-06-29 07:45:27";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:7:"user_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:1;O:26:"Botble\ACL\Models\UserMeta":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:9:"user_meta";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:2;s:3:"key";s:15:"minimal_sidebar";s:5:"value";s:2:"no";s:7:"user_id";i:1;s:10:"created_at";s:19:"2025-06-27 12:48:46";s:10:"updated_at";s:19:"2025-06-28 17:55:11";}s:11:" * original";a:6:{s:2:"id";i:2;s:3:"key";s:15:"minimal_sidebar";s:5:"value";s:2:"no";s:7:"user_id";i:1;s:10:"created_at";s:19:"2025-06-27 12:48:46";s:10:"updated_at";s:19:"2025-06-28 17:55:11";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:7:"user_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:2;O:26:"Botble\ACL\Models\UserMeta":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:9:"user_meta";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:3;s:3:"key";s:6:"locale";s:5:"value";N;s:7:"user_id";i:1;s:10:"created_at";s:19:"2025-06-28 17:57:31";s:10:"updated_at";s:19:"2025-06-28 17:57:31";}s:11:" * original";a:6:{s:2:"id";i:3;s:3:"key";s:6:"locale";s:5:"value";N;s:7:"user_id";i:1;s:10:"created_at";s:19:"2025-06-28 17:57:31";s:10:"updated_at";s:19:"2025-06-28 17:57:31";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:7:"user_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:3;O:26:"Botble\ACL\Models\UserMeta":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:9:"user_meta";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:4;s:3:"key";s:16:"locale_direction";s:5:"value";s:3:"ltr";s:7:"user_id";i:1;s:10:"created_at";s:19:"2025-06-28 17:57:31";s:10:"updated_at";s:19:"2025-06-28 17:57:31";}s:11:" * original";a:6:{s:2:"id";i:4;s:3:"key";s:16:"locale_direction";s:5:"value";s:3:"ltr";s:7:"user_id";i:1;s:10:"created_at";s:19:"2025-06-28 17:57:31";s:10:"updated_at";s:19:"2025-06-28 17:57:31";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:7:"user_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}